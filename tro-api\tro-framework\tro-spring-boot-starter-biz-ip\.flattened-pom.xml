<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.zhuoxiang.boot</groupId>
    <artifactId>tro-framework</artifactId>
    <version>2.2.0-snapshot</version>
  </parent>
  <artifactId>tro-spring-boot-starter-biz-ip</artifactId>
  <version>2.2.0-snapshot</version>
  <name>${project.artifactId}</name>
  <description>IP 拓展，支持如下功能：
        1. IP 功能：查询 IP 对应的城市信息
            基于 https://gitee.com/lionsoul/ip2region 实现
        2. 城市功能：查询城市编码对应的城市信息
            基于 https://github.com/modood/Administrative-divisions-of-China 实现</description>
  <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>
  <dependencies>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-common</artifactId>
    </dependency>
    <dependency>
      <groupId>org.lionsoul</groupId>
      <artifactId>ip2region</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
