package cn.zhuoxiang.tro.module.tro.convert;

import cn.zhuoxiang.tro.module.tro.dal.dataobject.servicepackageorder.PackageOrderLogDO;
import cn.zhuoxiang.tro.module.tro.service.servicepackageorder.bo.PackageOrderLogCreateReqBO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-06T12:14:02-0400",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 23.0.1 (Oracle Corporation)"
)
public class PackageOrderLogConvertImpl implements PackageOrderLogConvert {

    @Override
    public PackageOrderLogDO convert(PackageOrderLogCreateReqBO bean) {
        if ( bean == null ) {
            return null;
        }

        PackageOrderLogDO.PackageOrderLogDOBuilder packageOrderLogDO = PackageOrderLogDO.builder();

        packageOrderLogDO.userId( bean.getUserId() );
        packageOrderLogDO.userType( bean.getUserType() );
        packageOrderLogDO.orderId( bean.getOrderId() );
        packageOrderLogDO.beforeStatus( bean.getBeforeStatus() );
        packageOrderLogDO.afterStatus( bean.getAfterStatus() );
        packageOrderLogDO.operateType( bean.getOperateType() );
        packageOrderLogDO.content( bean.getContent() );

        return packageOrderLogDO.build();
    }
}
