package cn.zhuoxiang.tro.framework.signature.bo.jzq;

import lombok.Data;

import java.io.File;

/**
 * 重新申请实名认证请求参数
 */
@Data
public class OrganizationReapplyRequest {
    /**
     * 邮箱。必填，需保证唯一性，接口不对邮箱真实性做校验，符合邮箱规则即可。
     */
    private String emailOrMobile;

    /**
     * 公司名称。必填，若公司名称包含括号请使用中文括号。
     */
    private String name;

    /**
     * 组织类型。必填，0 表示企业，1 表示事业单位。
     */
    private Integer organizationType;

    /**
     * 证明类型。必填，0 表示多证，1 表示多证合一。
     */
    private Integer identificationType;

    /**
     * 营业执照号或事业单位事证号或统一社会信用代码。必填。
     */
    private String organizationRegNo;

    /**
     * 营业执照扫描件。必填，图片不能超过 2MB，接口不对传入的营业执照复印件图片进行真实性校验，需要开发者确保真实性。
     */
    private String organizationRegImg;

    /**
     * 法人姓名。必填。
     */
    private String legalName;

    /**
     * 法人身份证号。非必填，如果需要对法人进行认证（如人脸识别、银行卡认证等）时必传。
     */
    private String legalIdentityCard;

    /**
     * 法人电话号码。非必填，如果需要对法人进行认证（如运营商三要素、短信验证码等）时必传。
     */
    private String legalMobile;

    /**
     * 法人身份证正面图片。非必填，图片不能超过 2MB。
     */
    private String legalIdentityFrontImg;

    /**
     * 法人身份证反面图片。非必填，图片不能超过 2MB。
     */
    private String legalIdentityBackImg;

    /**
     * 公章签章图片。非必填，规格 180x180PX，透明背景，.png 格式，图片不能超过 2MB。可由系统生成。
     */
    private String signImg;

    /**
     * 法人住址。非必填。
     */
    private String address;

    /**
     * 授权人姓名。非必填，如果需要对授权人进行认证时必传。
     */
    private String authorizeName;

    /**
     * 授权人身份证。非必填，如果需要对授权人进行认证时必传。
     */
    private String authorizeCard;

    /**
     * 授权人手机号。非必填，如果需要对授权人进行认证时必传。
     */
    private String authorizeMobilePhone;

    /**
     * 组织结构代码。非必填，多证时必传。
     */
    private String organizationCode;

    /**
     * 组织结构代码扫描件。非必填，多证时必传，图片不能超过 2MB。需要开发者确保扫描件的真实性。
     */
    private String organizationCodeImg;

    /**
     * 税务登记扫描件。非必填，事业单位选填，其他多证时必传，图片不能超过 2MB。需要开发者确保扫描件的真实性。
     */
    private String taxCertificateImg;

    /**
     * 签约申请书扫描图。非必填，图片不能超过 2MB。需要开发者确保申请表的真实性。
     */
    private String signApplication;

}
