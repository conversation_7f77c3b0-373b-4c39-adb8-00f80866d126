package cn.zhuoxiang.tro.framework.signature.bo.jzq;

import lombok.Data;

/**
 * 个人证书申请资料上传请求参数
 */
@Data
public class CloudCertiPerInfoRequest {
    /**
     * 用户名称。
     * 必填项。
     */
    private String fullName;

    /**
     * 用户证件号。
     * 必填项。
     */
    private String identityCard;

    /**
     * 身份证人像面，图片大小控制在1M以内。
     * 必填项。
     */
    private String idenFront;

    /**
     * 身份证国徽面，图片大小控制在1M以内。
     * 必填项。
     */
    private String idenReverse;

    /**
     * 申请表，可以不用传。
     * 可选项。
     */
    private byte[] applyTable;

    /**
     * 证书sn。
     * 可选项。
     */
    private String certSn;

    /**
     * 第三方核验流水号。
     * 可选项。
     */
    private String busId;

    /**
     * 第三方核验请求参数（JSON 字符串格式）。
     * 可选项。
     */
    private String requestParam;

    /**
     * 第三方核验响应参数（JSON 字符串格式）。
     * 可选项。
     */
    private String resultParam;

    /**
     * 核验时间，格式建议为(yyyy-MM-dd HH:mm:ss) 或 (yyyyMMddHHmmss)。
     * 可选项。
     */
    private String verifyTime;

}
