package cn.zhuoxiang.tro.module.infra.api.config.dto;

import lombok.Data;
import lombok.ToString;

/**
 * 参数配置表
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ConfigDTO {

    /**
     * 参数主键
     */
    private Long id;
    /**
     * 参数分类
     */
    private String category;
    /**
     * 参数名称
     */
    private String name;
    /**
     * 参数键名
     * <p>
     * 支持多 DB 类型时，无法直接使用 key + @TableField("config_key") 来实现转换，原因是 "config_key" AS key 而存在报错
     */
    private String configKey;
    /**
     * 参数键值
     */
    private String value;

}
