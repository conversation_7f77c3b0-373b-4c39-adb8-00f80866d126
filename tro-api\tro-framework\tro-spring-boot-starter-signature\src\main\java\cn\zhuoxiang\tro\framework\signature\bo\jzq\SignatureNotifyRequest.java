package cn.zhuoxiang.tro.framework.signature.bo.jzq;

import lombok.Data;

/**
 * 发起签约提醒请求参数
 * 包含申请号、用户姓名、身份证信息、身份类型和通知类型等信息。
 */
@Data
public class SignatureNotifyRequest {
    /**
     * 申请号
     */
    private String applyNo;

    /**
     * 用户的全名
     */
    private String fullName;

    /**
     * 用户的身份证号
     */
    private String identityCard;

    /**
     * 用户的身份类型（例如身份证类型）
     */
    private String identityType;

    /**
     * 签署操作通知类型（例如是否异步通知）
     */
    private String signNotifyType;
}
