<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.zhuoxiang.boot</groupId>
    <artifactId>tro-framework</artifactId>
    <version>2.2.0-snapshot</version>
  </parent>
  <artifactId>tro-spring-boot-starter-signature</artifactId>
  <version>2.2.0-snapshot</version>
  <name>${project.artifactId}</name>
  <description>在线签约模块</description>
  <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>
  <dependencies>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-common</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.junziqian</groupId>
      <artifactId>sdk</artifactId>
      <version>2.2.7</version>
    </dependency>
  </dependencies>
</project>
