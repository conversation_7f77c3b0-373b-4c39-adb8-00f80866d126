package cn.zhuoxiang.tro.framework.signature.bo.jzq;

import lombok.Data;

/**
 * 获取签约链接请求参数
 * 包含申请号、手动签署对象的姓名、身份证信息、身份类型等信息。
 */
@Data
public class SignatureLinkRequest {
    /**
     * 申请号，发起合同签署接口返回的 APL 编号
     */
    private String applyNo;

    /**
     * 手动签署对象的姓名
     */
    private String fullName;

    /**
     * 手动签署对象的证件号
     */
    private String identityCard;

    /**
     * 证件类型：个人为 1，企业为 11
     */
    private int identityType;
}
