<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>tro-spring-boot-starter-signature</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>在线签约模块</description>
    <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>

    <dependencies>
        <dependency>
            <groupId>cn.zhuoxiang.boot</groupId>
            <artifactId>tro-common</artifactId>
        </dependency>

        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>


        <!-- Test 测试相关 -->
        <dependency>
            <groupId>cn.zhuoxiang.boot</groupId>
            <artifactId>tro-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.junziqian</groupId>
            <artifactId>sdk</artifactId>
            <version>2.2.7</version>
        </dependency>
    </dependencies>

</project>
