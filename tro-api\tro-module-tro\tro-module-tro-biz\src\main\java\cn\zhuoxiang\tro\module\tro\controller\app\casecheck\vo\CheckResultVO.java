package cn.zhuoxiang.tro.module.tro.controller.app.casecheck.vo;

import lombok.Data;
import java.util.List;

@Data
public class CheckResultVO {
    // ========== Old format fields (for backward compatibility) ==========
    /**
     * 类型 (Old format: "type")
     * @deprecated Use ipType instead
     */
    @Deprecated
    private String type;

    /**
     * 相关信息 (Old format: "ip_owner")
     */
    private String ipOwner;

    /**
     * 风险说明 (Old format: "risk_description")
     */
    private String riskDescription;

    /**
     * 图片 (Old format: "ip_image")
     * COS: checks/check_id/result/image.jpg
     * checks/{check_id}/results/{filename}
     * @deprecated Use ipAssetUrls instead
     */
    @Deprecated
    private String ipImage;

    /**
     * 报告 (Old format: "report")
     */
    private String report;

    // ========== New format fields ==========
    /**
     * IP类型 (New format: "ip_type")
     * e.g., "Copyright", "Trademark"
     */
    private String ipType;

    /**
     * IP资产URLs (New format: "ip_asset_urls")
     * Array of URLs for IP assets
     */
    private List<String> ipAssetUrls;

    /**
     * 文本内容 (New format: "text")
     * Generic text content for any IP type
     */
    private String text;

    /**
     * 产品URL (New format: "product_url")
     * URL of the product image/query image
     * Can be a single string or array (for backward compatibility)
     */
    private String productUrl;

    /**
     * 原告ID (New format: "plaintiff_id")
     * Can be integer or null
     */
    private Integer plaintiffId;

    /**
     * 原告姓名 (New format: "plaintiff_name")
     */
    private String plaintiffName;

    /**
     * 注册号 (New format: "reg_no")
     * Can be a single string or array (for backward compatibility)
     */
    private String regNo;

    /**
     * 最后案件档案号 (New format: "last_case_docket")
     */
    private String lastCaseDocket;

    /**
     * 案件数量 (New format: "number_of_cases")
     */
    private Integer numberOfCases;

    /**
     * 最后案件提交日期 (New format: "last_case_date_filed")
     */
    private String lastCaseDateFiled;

    // ========== Computed properties for display ==========

    /**
     * Get the display type (prioritize new format)
     */
    public String getDisplayType() {
        return ipType != null ? ipType : type;
    }

    /**
     * Get the display registration number
     */
    public String getDisplayRegNo() {
        if (regNo != null && !"None".equals(regNo)) {
            return regNo;
        }
        return "None";
    }

    /**
     * Check if this result has been used in TRO cases
     */
    public boolean hasBeenUsedInTro() {
        return plaintiffId != null;
    }

    /**
     * Get display product URL (handle both string and array formats)
     */
    public String getDisplayProductUrl() {
        return productUrl;
    }

    /**
     * Get display IP asset URLs (handle both old ipImage and new ipAssetUrls)
     */
    public List<String> getDisplayIpAssetUrls() {
        if (ipAssetUrls != null && !ipAssetUrls.isEmpty()) {
            return ipAssetUrls;
        }
        // Fallback to old format if available
        if (ipImage != null) {
            return List.of(ipImage);
        }
        return List.of();
    }
}
