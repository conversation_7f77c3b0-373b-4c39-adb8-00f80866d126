package cn.zhuoxiang.tro.framework.signature.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 创建策略工厂或上下文类
 */
@Service
@Slf4j
public class SignChannelContext {

    private final Map<String, SignChannelStrategy> strategyMap = new ConcurrentHashMap<>();


    @Autowired
    public SignChannelContext(List<SignChannelStrategy> strategies) {
        if (strategies == null || strategies.isEmpty()) {
            log.warn("没有找到合同签署的策略实现");
        } else {
            log.info("策略列表大小：" + strategies.size());
            strategyMap.clear();
            for (SignChannelStrategy payStrategy : strategies) {
                strategyMap.put(payStrategy.getChannel(), payStrategy);
            }
        }
    }

    public SignChannelStrategy getStrategy(String channel) {
        return strategyMap.get(channel);
    }
}
