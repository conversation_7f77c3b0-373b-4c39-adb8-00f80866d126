package cn.zhuoxiang.tro.framework.common.util.file;

import org.apache.poi.xwpf.usermodel.*;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * word模板文件操作工具类
 */
public class WordUtils {
    /**
     * 将word中某些标签替换成指定的值，并生成一个新的word文档。
     *
     * @param templateFilePath word模板文件路径
     * @param outFilePath      填充后输出文件路径
     * @param map              key:word中的占位标签，value对应标签要替换的值。
     * @throws IOException
     */
    public static void insertAndOutFile(String templateFilePath, String outFilePath, Map<String, String> map) throws IOException {
        //准备工作，生成docx对象
        String templatePath = templateFilePath;
        InputStream is = new FileInputStream(templatePath);
        XWPFDocument docx = new XWPFDocument(is);
        //获取表格
        List<XWPFTable> tables = docx.getTables();
        //定位到第一个表格
        XWPFTable table = tables.get(0);
        //遍历该表格所有的行
        for (int i = 0; i < table.getRows().size(); i++) {
            XWPFTableRow row = table.getRow(i);
            //遍历该行所有的列
            for (int j = 0; j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getTableCells().get(j);
                //获取该格子里所有的段
                List<XWPFParagraph> paragraphs = cell.getParagraphs();
                for (XWPFParagraph p : paragraphs) {
                    //遍历该格子里的段
                    List<XWPFRun> runs = p.getRuns();
                    for (XWPFRun run : runs) {
                        //遍历该段里的所有文本
                        String str = run.toString();
                        //如果该段文本包含map中的key，则替换为map中的value值。
                        Set<String> keySet = map.keySet();
                        for (String key : keySet) {
                            System.out.println(str);
                            if (str.trim().equals(key)) {
                                //替换该文本0位置的数据。
                                run.setText(map.get(key), 0);
                            }
                        }
                    }
                }
            }
        }
        //输出
        OutputStream os = new FileOutputStream(outFilePath);
        docx.write(os);
        is.close();
        os.close();
    }
}
