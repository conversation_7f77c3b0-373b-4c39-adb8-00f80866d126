<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.zhuoxiang.boot</groupId>
    <artifactId>tro-module-infra</artifactId>
    <version>2.2.0-snapshot</version>
  </parent>
  <artifactId>tro-module-infra-api</artifactId>
  <version>2.2.0-snapshot</version>
  <name>${project.artifactId}</name>
  <description>infra 模块 API，暴露给其它模块调用</description>
  <dependencies>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-common</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>
</project>
