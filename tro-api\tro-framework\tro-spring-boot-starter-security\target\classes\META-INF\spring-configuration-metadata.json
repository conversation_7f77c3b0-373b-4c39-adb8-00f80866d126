{"groups": [{"name": "tro.security", "type": "cn.zhuoxiang.tro.framework.security.config.SecurityProperties", "sourceType": "cn.zhuoxiang.tro.framework.security.config.SecurityProperties"}], "properties": [{"name": "tro.security.mock-enable", "type": "java.lang.Bo<PERSON>an", "description": "mock 模式的开关", "sourceType": "cn.zhuoxiang.tro.framework.security.config.SecurityProperties"}, {"name": "tro.security.mock-secret", "type": "java.lang.String", "description": "mock 模式的密钥 一定要配置密钥，保证安全性", "sourceType": "cn.zhuoxiang.tro.framework.security.config.SecurityProperties"}, {"name": "tro.security.password-encoder-length", "type": "java.lang.Integer", "description": "PasswordEncoder 加密复杂度，越高开销越大", "sourceType": "cn.zhuoxiang.tro.framework.security.config.SecurityProperties"}, {"name": "tro.security.permit-all-urls", "type": "java.util.List<java.lang.String>", "description": "免登录的 URL 列表", "sourceType": "cn.zhuoxiang.tro.framework.security.config.SecurityProperties"}, {"name": "tro.security.token-header", "type": "java.lang.String", "description": "HTTP 请求时，访问令牌的请求 Header", "sourceType": "cn.zhuoxiang.tro.framework.security.config.SecurityProperties"}, {"name": "tro.security.token-parameter", "type": "java.lang.String", "description": "HTTP 请求时，访问令牌的请求参数 初始目的：解决 WebSocket 无法通过 header 传参，只能通过 token 参数拼接", "sourceType": "cn.zhuoxiang.tro.framework.security.config.SecurityProperties"}], "hints": []}