package cn.zhuoxiang.tro.module.infra.dal.mysql.file;

import cn.zhuoxiang.tro.framework.common.pojo.PageResult;
import cn.zhuoxiang.tro.framework.mybatis.core.mapper.BaseMapperX;
import cn.zhuoxiang.tro.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.zhuoxiang.tro.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import cn.zhuoxiang.tro.module.infra.dal.dataobject.file.FileDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 文件操作 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileMapper extends BaseMapperX<FileDO> {

    default PageResult<FileDO> selectPage(FilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileDO>()
                .likeIfPresent(FileDO::getPath, reqVO.getPath())
                .likeIfPresent(FileDO::getType, reqVO.getType())
                .betweenIfPresent(FileDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileDO::getId));
    }

}
