package cn.zhuoxiang.tro.framework.signature.utils;

import com.junziqian.sdk.util.exception.ResultInfoException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;

@Slf4j
public class JunziqianUtils {

    /**
     * 默认加密方式:不输入使用sha256,其它可选择项md5,sha1,sha3-256
     */
    private static final String encryMethod = "sha256";
    /**
     * 默认ts单位:1毫秒,2秒
     */
    private static final byte tsType = (byte) 1;

    /**
     * 填充签名数据
     *
     * @param req
     */
    public static void fillSign(String appkey, String appSecret, Map<String, Object> req) {
        /**默认加密方式:不输入使用sha256,其它可选择项md5,sha1,sha3-256*/
        long ts = System.currentTimeMillis();
        if (tsType == 2) {
            ts = System.currentTimeMillis() / 1000;
        }
        String sign;
        String nonce = DigestUtils.md5Hex(System.currentTimeMillis() + "");
        String signSrc = "nonce" + nonce + "ts" + ts + "app_key" + appkey + "app_secret" + appSecret;
        if (encryMethod == null || encryMethod.equalsIgnoreCase("sha256")) {
            sign = DigestUtils.sha256Hex(signSrc);
        } else if (encryMethod.equalsIgnoreCase("sha1")) {
            sign = DigestUtils.sha1Hex(signSrc);
        } else if (encryMethod.equalsIgnoreCase("md5")) {
            sign = DigestUtils.md5Hex(signSrc);
        } else if (encryMethod.equalsIgnoreCase("sha3-256")) {
            sign = DigestUtils.sha3_256Hex(signSrc);//*此需要引入加密商BouncyCastleProvider才能使用
        } else {
            throw new ResultInfoException("HTTP_PARAM_ERROR", encryMethod + ",必须为md5,sha1,sha256,sha3-256之一");
        }
        req.put("ts", ts);
        req.put("app_key", appkey);
        req.put("sign", sign);
        //这只只是为了生成一个随机值
        req.put("nonce", nonce);
        if (encryMethod != null) {
            //为''也不会传,在requestUtils中有判断
            req.put("encry_method", encryMethod);
        }
    }

    /*public static void main(String[] args) {
        //SHA3-256 必须指定加密提供商为//BouncyCastleProvider
        RequestUtils requestUtils = RequestUtils.init("https://api.sandbox.junziqian.com", "c6940e4ae8073d43", "********************************");
        //建议生成为spring bean
        ResultInfo<Void> ri = requestUtils.doPost("/v2/ping");
        log.info("请求结果:" + JSONObject.toJSONString(ri));
        Assert.notNull(ri, "请求失败,返回结果为空。");
        Assert.isTrue(ri.isSuccess(), "请求失败:" + ri.getMsg());
        //{"msg":"app_secret不可用","resultCode":"HTTP_PARAM_ERROR","success":false}
        //{"success":true}

        //构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("emailOrMobile", "<EMAIL>");
        ResultInfo<JSONObject> ri2 = requestUtils.doPost("/v2/user/organizationAuditStatus", params);
        log.info("请求结果:" + JSONObject.toJSONString(ri2));

        params = new HashMap<>();
        String url = "https://api.sandbox.junziqian.com/v2/user/organizationCreate";
        params.put("emailOrMobile", "<EMAIL>"); //邮箱
        params.put("name", "贵州数智纵横科技有限公司"); //企业名称
        params.put("organizationType", "0"); //企业类型
        params.put("identificationType", "1"); //证件类型
        params.put("organizationRegNo", "91520115MA7MF3XJ5F"); //营业执照号
        params.put("organizationRegImg", new File("/Users/<USER>/Documents/卓翔科技/数字纵横资料/营业执照2.png"));//营业执照图片
        params.put("legalName","何伟");//法人姓名
        //params.put("legalIdentityCard", "50022XXXXXX28");//法人身份证号
        //params.put("legalMobile", "1862XXXXX71");//法人手机号
        fillSign("c6940e4ae8073d43", "********************************", params);
        String str = HttpClientUtils.init().getPost(url, null, params, true);
        System.out.println(str);
    }*/
}
