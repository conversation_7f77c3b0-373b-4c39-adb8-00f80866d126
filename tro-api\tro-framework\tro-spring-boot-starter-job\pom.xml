<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>tro-spring-boot-starter-job</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>任务拓展
        1. 定时任务，基于 Quartz 拓展
        2. 异步任务，基于 Spring Async 拓展
    </description>
    <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>

    <dependencies>
        <dependency>
            <groupId>cn.zhuoxiang.boot</groupId>
            <artifactId>tro-common</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

    </dependencies>

</project>
