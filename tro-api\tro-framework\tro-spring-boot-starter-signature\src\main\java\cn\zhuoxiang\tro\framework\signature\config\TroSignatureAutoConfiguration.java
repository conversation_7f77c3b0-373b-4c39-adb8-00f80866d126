package cn.zhuoxiang.tro.framework.signature.config;

import cn.zhuoxiang.tro.framework.signature.core.SignChannelContext;
import cn.zhuoxiang.tro.framework.signature.core.SignChannelStrategy;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import java.util.List;

@AutoConfiguration
@EnableConfigurationProperties({JunziqianConfig.class})
public class TroSignatureAutoConfiguration {
    @Bean
    public SignChannelContext signChannelContext(List<SignChannelStrategy> strategies) {
        return new SignChannelContext(strategies);
    }
}
