{"groups": [{"name": "tro.swagger", "type": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.web", "type": "cn.zhuoxiang.tro.framework.web.config.WebProperties", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties"}, {"name": "tro.web.admin-api", "type": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties", "sourceMethod": "public cn.zhuoxiang.tro.framework.web.config.WebProperties.Api getAdminApi() "}, {"name": "tro.web.admin-ui", "type": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Ui", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties", "sourceMethod": "public cn.zhuoxiang.tro.framework.web.config.WebProperties.Ui getAdminUi() "}, {"name": "tro.web.app-api", "type": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties", "sourceMethod": "public cn.zhuoxiang.tro.framework.web.config.WebProperties.Api getAppApi() "}, {"name": "tro.web.open-api", "type": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties", "sourceMethod": "public cn.zhuoxiang.tro.framework.web.config.WebProperties.Api getOpenApi() "}, {"name": "tro.xss", "type": "cn.zhuoxiang.tro.framework.xss.config.XssProperties", "sourceType": "cn.zhuoxiang.tro.framework.xss.config.XssProperties"}], "properties": [{"name": "tro.swagger.author", "type": "java.lang.String", "description": "作者", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.swagger.description", "type": "java.lang.String", "description": "描述", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.swagger.email", "type": "java.lang.String", "description": "email", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.swagger.license", "type": "java.lang.String", "description": "license", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.swagger.license-url", "type": "java.lang.String", "description": "license-url", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.swagger.title", "type": "java.lang.String", "description": "标题", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.swagger.url", "type": "java.lang.String", "description": "url", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.swagger.version", "type": "java.lang.String", "description": "版本", "sourceType": "cn.zhuoxiang.tro.framework.swagger.config.SwaggerProperties"}, {"name": "tro.web.admin-api.controller", "type": "java.lang.String", "description": "Controller 所在包的 Ant 路径规则 主要目的是，给该 Controller 设置指定的 {@link #prefix}", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api"}, {"name": "tro.web.admin-api.prefix", "type": "java.lang.String", "description": "API 前缀，实现所有 Controller 提供的 RESTFul API 的统一前缀 意义：通过该前缀，避免 Swagger、Actuator 意外通过 Nginx 暴露出来给外部，带来安全性问题 这样，Nginx 只需要配置转发到 /api/* 的所有接口即可。 @see TroWebAutoConfiguration#configurePathMatch(PathMatchConfigurer)", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api"}, {"name": "tro.web.admin-ui.url", "type": "java.lang.String", "description": "访问地址", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Ui"}, {"name": "tro.web.app-api.controller", "type": "java.lang.String", "description": "Controller 所在包的 Ant 路径规则 主要目的是，给该 Controller 设置指定的 {@link #prefix}", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api"}, {"name": "tro.web.app-api.prefix", "type": "java.lang.String", "description": "API 前缀，实现所有 Controller 提供的 RESTFul API 的统一前缀 意义：通过该前缀，避免 Swagger、Actuator 意外通过 Nginx 暴露出来给外部，带来安全性问题 这样，Nginx 只需要配置转发到 /api/* 的所有接口即可。 @see TroWebAutoConfiguration#configurePathMatch(PathMatchConfigurer)", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api"}, {"name": "tro.web.open-api.controller", "type": "java.lang.String", "description": "Controller 所在包的 Ant 路径规则 主要目的是，给该 Controller 设置指定的 {@link #prefix}", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api"}, {"name": "tro.web.open-api.prefix", "type": "java.lang.String", "description": "API 前缀，实现所有 Controller 提供的 RESTFul API 的统一前缀 意义：通过该前缀，避免 Swagger、Actuator 意外通过 Nginx 暴露出来给外部，带来安全性问题 这样，Nginx 只需要配置转发到 /api/* 的所有接口即可。 @see TroWebAutoConfiguration#configurePathMatch(PathMatchConfigurer)", "sourceType": "cn.zhuoxiang.tro.framework.web.config.WebProperties$Api"}, {"name": "tro.xss.enable", "type": "java.lang.Bo<PERSON>an", "description": "是否开启，默认为 true", "sourceType": "cn.zhuoxiang.tro.framework.xss.config.XssProperties"}, {"name": "tro.xss.exclude-urls", "type": "java.util.List<java.lang.String>", "description": "需要排除的 URL，默认为空", "sourceType": "cn.zhuoxiang.tro.framework.xss.config.XssProperties"}], "hints": []}