package cn.zhuoxiang.tro.module.infra.api.config;

import cn.zhuoxiang.tro.framework.common.util.object.BeanUtils;
import cn.zhuoxiang.tro.module.infra.api.config.dto.ConfigDTO;
import cn.zhuoxiang.tro.module.infra.dal.dataobject.config.ConfigDO;
import cn.zhuoxiang.tro.module.infra.service.config.ConfigService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 参数配置 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConfigApiImpl implements ConfigApi {

    @Resource
    private ConfigService configService;

    @Override
    public String getConfigValueByKey(String key) {
        ConfigDO config = configService.getConfigByKey(key);
        return config != null ? config.getValue() : null;
    }

    @Override
    public ConfigDTO getConfigKey(String key) {
        ConfigDO config = configService.getConfigByKey(key);
        return BeanUtils.toBean(config, ConfigDTO.class);
    }

}
