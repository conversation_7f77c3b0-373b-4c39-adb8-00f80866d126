<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.zhuoxiang.boot</groupId>
    <artifactId>tro-framework</artifactId>
    <version>2.2.0-snapshot</version>
  </parent>
  <artifactId>tro-spring-boot-starter-websocket</artifactId>
  <version>2.2.0-snapshot</version>
  <name>${project.artifactId}</name>
  <description>WebSocket 框架，支持多节点的广播</description>
  <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>
  <dependencies>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-common</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-spring-boot-starter-security</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-spring-boot-starter-mq</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.kafka</groupId>
      <artifactId>spring-kafka</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.amqp</groupId>
      <artifactId>spring-rabbit</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-spring-boot-starter</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-spring-boot-starter-biz-tenant</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
</project>
