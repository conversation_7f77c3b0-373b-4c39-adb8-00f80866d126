package cn.zhuoxiang.tro.framework.signature.bo.jzq;

import lombok.Data;

/**
 * 签约方（企业）
 */
@Data
public class OrgSignatoriesRequest {
    //姓名
    private String fullName;

    //身份类型:1身份证,2护照,3台胞证,4港澳居民来往内地通行证,11营业执照,12统一社会信用代码
    private Integer identityType = 11;

    //营业执照号
    private String identityCard;

    //在君子签注册认证的邮箱
    private String email;

    //在君子签注册手机号
    private String mobile;

    //坐标（X Y）定位签字位置
    private String chapteJson;

    //为按关键字查找到的签字位置做偏移使用的参数
    private SearchConvertExtend searchConvertExtend;

    //取消签约前短信校验
    private Integer noNeedVerify = 1;

    //是否使用自动签署完成，0或null不使用，1自动(当且只当合同处理方式为部份自动或收集批量签时有效)
    private Integer serverCaAuto = 1;

    //签字类型，标准图形章或公章:0标准图形章,1公章或手写,2公章手写或手写,3个人方形标准章（用户类型是个人且姓名2-4个字符生效，其他情况默认使用系统标准图形章）
    private Integer signLevel = 0;

    //签字位置-按关键字签署，positionType=2时必须传入，支持多个关键字以英文分号(;)分隔
    private String searchKey;
}
