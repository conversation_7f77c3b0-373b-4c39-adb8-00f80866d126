package cn.zhuoxiang.tro.framework.common.util.date;

import cn.hutool.core.date.LocalDateTimeUtil;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils {

    /**
     * 时区 - 默认
     */
    public static final String TIME_ZONE_DEFAULT = "GMT+8";

    /**
     * 秒转换成毫秒
     */
    public static final long SECOND_MILLIS = 1000;

    public static final String FORMAT_YEAR_MONTH_DAY = "yyyy-MM-dd";

    public static final String FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND = "yyyy-MM-dd HH:mm:ss";

    // 日期格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 将 LocalDateTime 转换成 Date
     *
     * @param date LocalDateTime
     * @return LocalDateTime
     */
    public static Date of(LocalDateTime date) {
        if (date == null) {
            return null;
        }
        // 将此日期时间与时区相结合以创建 ZonedDateTime
        ZonedDateTime zonedDateTime = date.atZone(ZoneId.systemDefault());
        // 本地时间线 LocalDateTime 到即时时间线 Instant 时间戳
        Instant instant = zonedDateTime.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        return Date.from(instant);
    }

    /**
     * 将 Date 转换成 LocalDateTime
     *
     * @param date Date
     * @return LocalDateTime
     */
    public static LocalDateTime of(Date date) {
        if (date == null) {
            return null;
        }
        // 转为时间戳
        Instant instant = date.toInstant();
        // UTC时间(世界协调时间,UTC + 00:00)转北京(北京,UTC + 8:00)时间
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static Date addTime(Duration duration) {
        return new Date(System.currentTimeMillis() + duration.toMillis());
    }

    public static boolean isExpired(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        return now.isAfter(time);
    }

    /**
     * 创建指定时间
     *
     * @param year  年
     * @param mouth 月
     * @param day   日
     * @return 指定时间
     */
    public static Date buildTime(int year, int mouth, int day) {
        return buildTime(year, mouth, day, 0, 0, 0);
    }

    /**
     * 创建指定时间
     *
     * @param year   年
     * @param mouth  月
     * @param day    日
     * @param hour   小时
     * @param minute 分钟
     * @param second 秒
     * @return 指定时间
     */
    public static Date buildTime(int year, int mouth, int day,
                                 int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, mouth - 1);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, 0); // 一般情况下，都是 0 毫秒
        return calendar.getTime();
    }

    public static Date max(Date a, Date b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.compareTo(b) > 0 ? a : b;
    }

    public static LocalDateTime max(LocalDateTime a, LocalDateTime b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.isAfter(b) ? a : b;
    }

    /**
     * 判断是否是当前年日期
     */
    public static boolean isDateInCurrentYear(LocalDate date) {
        LocalDate now = LocalDate.now(); // 获取当前日期
        return date.getYear() == now.getYear(); // 判断年份是否相同
    }

    /**
     * 是否今天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isToday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now());
    }

    /**
     * 是否昨天
     *
     * @param date 日期
     * @return 是否
     */
    public static boolean isYesterday(LocalDateTime date) {
        return LocalDateTimeUtil.isSameDay(date, LocalDateTime.now().minusDays(1));
    }

    /**
     * 获取当前日期并格式化为yyyy-MM-dd
     *
     * @return 当前日期的字符串格式
     */
    public static String getCurrentDate() {
        return formatDate(LocalDate.now());
    }

    /**
     * 获取多少月后的日期并格式化为yyyy-MM-dd
     *
     * @return 半年后日期的字符串格式
     */
    public static String getXMonthLater(Integer number) {
        return formatDate(LocalDate.now().plusMonths(number));
    }

    /**
     * 获取半年后的日期并格式化为yyyy-MM-dd
     *
     * @return 半年后日期的字符串格式
     */
    public static String getHalfYearLater() {
        return formatDate(LocalDate.now().plusMonths(6));
    }

    /**
     * 一个季度后的日期并格式化为yyyy-MM-dd
     *
     * @return 半年后日期的字符串格式
     */
    public static String getOneQuarterLater() {
        return formatDate(LocalDate.now().plusMonths(3));
    }

    /**
     * 获取一年后的日期并格式化为yyyy-MM-dd
     *
     * @return 一年后日期的字符串格式
     */
    public static String getOneYearLater() {
        return formatDate(LocalDate.now().plusYears(1));
    }


    /**
     * 获取N年后的日期并格式化为yyyy-MM-dd
     *
     * @return 一年后日期的字符串格式
     */
    public static String getNYearLater(Integer number) {
        return formatDate(LocalDate.now().plusYears(number));
    }

    /**
     * 获取两年后的日期并格式化为yyyy-MM-dd
     *
     * @return 两年后日期的字符串格式
     */
    public static String getTwoYearsLater() {
        return formatDate(LocalDate.now().plusYears(2));
    }

    // 私有方法：格式化LocalDate为yyyy-MM-dd字符串
    public static String formatDate(LocalDate date) {
        return date.format(DATE_FORMATTER);
    }

    public static String formatDateTime(LocalDateTime dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
        return dateTime.format(formatter);
    }

    public static LocalDate subtractDaysFromDate(LocalDate date, int daysToSubtract) {
        // 从原始日期减去指定天数，返回新的日期
        return date.minusDays(daysToSubtract);
    }

}
