<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.zhuoxiang.boot</groupId>
    <artifactId>tro-framework</artifactId>
    <version>2.2.0-snapshot</version>
  </parent>
  <artifactId>tro-spring-boot-starter-security</artifactId>
  <version>2.2.0-snapshot</version>
  <name>${project.artifactId}</name>
  <description>1. security：用户的认证、权限的校验，实现「谁」可以做「什么事」
        2. operatelog：操作日志，实现「谁」在「什么时间」对「什么」做了「什么事」</description>
  <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>
  <dependencies>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-common</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>io.github.mouzt</groupId>
      <artifactId>bizlog-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-module-system-api</artifactId>
      <version>${revision}</version>
    </dependency>
  </dependencies>
</project>
