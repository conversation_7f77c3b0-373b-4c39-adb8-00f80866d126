{"table": {"id": 1, "scene": 1, "parentMenuId": 888, "tableName": "infra_student", "tableComment": "学生表", "moduleName": "infra", "businessName": "demo", "className": "InfraStudent", "classComment": "学生", "author": "芋道源码"}, "columns": [{"id": 100, "columnName": "id", "dataType": "BIGINT", "columnComment": "编号", "primaryKey": true, "javaType": "<PERSON>", "javaField": "id", "example": "1024", "updateOperation": true, "listOperationResult": true}, {"columnName": "name", "dataType": "VARCHAR", "columnComment": "名字", "javaType": "String", "javaField": "name", "example": "芋头", "createOperation": true, "updateOperation": true, "listOperation": true, "listOperationCondition": "LIKE", "listOperationResult": true, "htmlType": "input"}, {"columnName": "description", "dataType": "VARCHAR", "columnComment": "简介", "javaType": "String", "javaField": "description", "example": "我是介绍", "createOperation": true, "updateOperation": true, "listOperationResult": true, "htmlType": "textarea"}, {"columnName": "birthday", "dataType": "DATE", "columnComment": "出生日期", "javaType": "LocalDateTime", "javaField": "birthday", "createOperation": true, "updateOperation": true, "listOperation": true, "listOperationCondition": "=", "listOperationResult": true, "htmlType": "datetime"}, {"columnName": "sex", "dataType": "INTEGER", "columnComment": "性别", "javaType": "Integer", "javaField": "sex", "dictType": "system_user_sex", "example": "1", "createOperation": true, "updateOperation": true, "listOperation": true, "listOperationCondition": "=", "listOperationResult": true, "htmlType": "select"}, {"columnName": "enabled", "dataType": "BOOLEAN", "columnComment": "是否有效", "javaType": "Boolean", "javaField": "enabled", "dictType": "infra_boolean_string", "example": "true", "createOperation": true, "updateOperation": true, "listOperation": true, "listOperationCondition": "=", "listOperationResult": true, "htmlType": "radio"}, {"columnName": "avatar", "dataType": "VARCHAR", "columnComment": "头像", "javaType": "String", "javaField": "avatar", "example": "https://www.iocoder.cn/1.png", "createOperation": true, "updateOperation": true, "listOperationResult": true, "htmlType": "imageUpload"}, {"columnName": "video", "dataType": "VARCHAR", "columnComment": "附件", "javaType": "String", "javaField": "video", "example": "https://www.iocoder.cn/1.mp4", "createOperation": true, "updateOperation": true, "listOperationResult": true, "htmlType": "fileUpload"}, {"columnName": "memo", "dataType": "VARCHAR", "columnComment": "备注", "javaType": "String", "javaField": "memo", "example": "我是备注", "createOperation": true, "updateOperation": true, "listOperationResult": true, "htmlType": "editor"}, {"columnName": "create_time", "dataType": "DATE", "columnComment": "创建时间", "nullable": true, "javaType": "LocalDateTime", "javaField": "createTime", "listOperation": true, "listOperationCondition": "BETWEEN", "listOperationResult": true, "htmlType": "datetime"}]}