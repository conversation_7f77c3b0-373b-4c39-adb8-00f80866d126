package cn.zhuoxiang.tro.framework.signature.bo.jzq;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

/**
 * 发起签约
 */
@Data
public class ApplySignRequest {
    //合同名称
    private String contractName;

    //使用云证书
    private Integer serverCa;

    //0或null默认.pdf; 1 ofd;2 word文件（传ofd和word文件发起合同时该参数必传）
    private Integer fileSuffix = 2;

    //指定合同发起方式（0上传PDF或者ofd文件;）
    private Integer fileType;

    //合同文件
    private String file;

    //合同文件
    private String url;

    //合同附近
    private JSONArray attachFiles;

    //指定签字位置类型（0签字坐标，1表单域，2关键字）
    private Integer positionType = 0;

    //指定合同文件签署方式（5为部分自动签，0手动签，1自动签，2只报全，6 HASH只保全，17收集信息批量签）
    private Integer dealType = 1;

    //ofd文件追加内容（0不能追加内容，1允许追加内容），允许追加内容时noEbqSign需要设置为2
    private Integer addPage = 0;

    //不显示ebq的保全章:1不显示但会签名,2不显示也不签名;0或其它-显示
    private Integer noEbqSign = 0;

    //是否使用骑缝章:1使用;2个人不要企业要，3个人要企业不要
    private Integer needQifengSign = 0;

    //合同签署完成后异步通知地址
    private String notifyUrl;

    //签约方 jsonArray
    private String signatories;
}
