<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.zhuoxiang.boot</groupId>
    <artifactId>tro-framework</artifactId>
    <version>2.2.0-snapshot</version>
  </parent>
  <artifactId>tro-spring-boot-starter-test</artifactId>
  <version>2.2.0-snapshot</version>
  <name>${project.artifactId}</name>
  <description>测试组件，用于单元测试、集成测试</description>
  <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>
  <dependencies>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-common</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-spring-boot-starter-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.zhuoxiang.boot</groupId>
      <artifactId>tro-spring-boot-starter-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.fppt</groupId>
      <artifactId>jedis-mock</artifactId>
    </dependency>
    <dependency>
      <groupId>uk.co.jemos.podam</groupId>
      <artifactId>podam</artifactId>
    </dependency>
  </dependencies>
</project>
