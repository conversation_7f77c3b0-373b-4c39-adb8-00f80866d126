package cn.zhuoxiang.tro.module.infra.controller.app.file;

import cn.hutool.core.io.IoUtil;
import cn.zhuoxiang.tro.framework.common.pojo.CommonResult;
import cn.zhuoxiang.tro.module.infra.controller.admin.file.vo.file.FileRespVO;
import cn.zhuoxiang.tro.module.infra.controller.admin.file.vo.file.FileUploadReqVO;
import cn.zhuoxiang.tro.module.infra.controller.app.file.vo.AppFileUploadReqVO;
import cn.zhuoxiang.tro.module.infra.service.file.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import static cn.zhuoxiang.tro.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - 文件存储")
@RestController
@RequestMapping("/infra/file")
@Validated
@Slf4j
public class AppFileController {

    @Resource
    private FileService fileService;

    @PostMapping("/upload")
    @Operation(summary = "上传文件")
    public CommonResult<String> uploadFile(AppFileUploadReqVO uploadReqVO) throws Exception {
        MultipartFile file = uploadReqVO.getFile();
        String path = uploadReqVO.getPath();
        return success(fileService.createFile(file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream())));
    }

    @PostMapping("/upload/v2")
    @Operation(summary = "上传文件v2", description = "模式一：后端上传文件")
    public CommonResult<FileRespVO> uploadFileV2(FileUploadReqVO uploadReqVO) throws Exception {
        MultipartFile file = uploadReqVO.getFile();
        String path = uploadReqVO.getPath();
        String fileName = uploadReqVO.getFileName();
        log.info("上传文件传递文件名字：{}", fileName);
        return success(fileService.createFile2(StringUtils.isNotBlank(fileName) ? fileName : file.getOriginalFilename(), path, IoUtil.readBytes(file.getInputStream())));
    }

}
