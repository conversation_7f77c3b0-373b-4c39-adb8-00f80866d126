package cn.zhuoxiang.tro.framework.signature.core.service;

import cn.zhuoxiang.tro.framework.signature.config.JunziqianConfig;
import cn.zhuoxiang.tro.framework.signature.config.JunziqianProperties;
import cn.zhuoxiang.tro.framework.signature.core.SignChannelStrategy;
import cn.zhuoxiang.tro.framework.signature.enums.SignatureChannelEnum;
import cn.zhuoxiang.tro.framework.signature.utils.JunziqianUtils;
import com.alibaba.fastjson.JSONObject;
import com.junziqian.sdk.bean.ResultInfo;
import com.junziqian.sdk.util.RequestUtils;
import com.junziqian.sdk.util.http.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static cn.hutool.crypto.digest.DigestUtil.sha1Hex;

/**
 * 君子签
 */
@Component
@Slf4j
public class JunziqianSignatureService implements SignChannelStrategy {

    @Autowired
    private JunziqianConfig junziqianConfig;


    @Override
    public String handle(String type, JSONObject json, JSONObject extend) {
        log.info("处理签约请求，类型: {}, 数据: {}, 扩展信息：{}", type, json, extend);
        switch (type) {
            case "organizationAuditStatus":
                log.info("企业实名认证状态查询");
                return organizationAuditStatus(json, extend);
            case "organizationCreate":
                log.info("企业实名认证上传");
                return organizationCreate(json, extend);
            case "organizationReapply":
                log.info("企业实名认证重传");
                return organizationReapply(json, extend);
            case "applySign":
                log.info("发起签约");
                return applySign(json, extend);
            case "link":
                log.info("获取签约链接");
                return link(json, extend);
            case "notify":
                log.info("发送签约提醒");
                return notify(json, extend);
            case "linkAnonyDetail":
                log.info("获取签约查看链接");
                return linkAnonyDetail(json, extend);
            case "linkFile":
                log.info("获取文件下载链接");
                return linkFile(json, extend);
            case "cancel":
                log.info("撤销签约合同");
                return cancel(json, extend);
            case "status":
                log.info("签约状态查询");
                return status(json, extend);
            case "userValid":
                log.info("二要素校验");
                return userValid(json, extend);
            case "entFourVerify":
                log.info("企业工商四要素核验");
                return entFourVerify(json, extend);
            case "cloudCertiPerInfo":
                log.info("个人证书申请资料上传");
                return cloudCertiPerInfo(json, extend);
            case "enterpriseInfoMatch":
                log.info("企业基本信息核验(企业工商三要素核验)");
                return enterpriseInfoMatch(json, extend);
            case "verifySign":
                log.info("验签");
                return verifySign(json, extend);
            default:
                throw new RuntimeException("不支持的参数");
        }
    }

    // --------------用户服务------------------------
    // 企业实名认证状态查询，查询是否审核成功。此接口为同步请求接口，也可以配置回调地址，由君子签异步返回审核结果。
    public String organizationAuditStatus(JSONObject json, JSONObject extend) {
        String emailOrMobile = json.getString("emailOrMobile");
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        RequestUtils requestUtils = RequestUtils.init(properties.getServicesUrl(), properties.getAppKey(), properties.getAppSecret());
        //构建请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("emailOrMobile", emailOrMobile);
        ResultInfo<JSONObject> ri = requestUtils.doPost("/v2/user/organizationAuditStatus", params);
        log.info("organizationAuditStatus 请求结果:" + JSONObject.toJSONString(ri));
        return JSONObject.toJSONString(ri);
    }

    //企业实名认证上传，参与签约的企业用户，需事先通过实名认证。该接口提供企业基本工商三要素认证能力，认证通过后，才能对企业发起合同签署；有涉及到签约的企业用户此接口必调。
    public String organizationCreate(JSONObject json, JSONObject extend) {
        Map<String, Object> params = json.getInnerMap();
        //params.put("notifyUrl", properties.getNotifyUrl());
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/user/organizationCreate", null, params, true);
        log.info("organizationCreate 请求结果:" + str);
        return str;
    }


    //企业实名认证重传，提交的企业信息审核不通过的情况下，修改后可以调用此接口重新提交。
    public String organizationReapply(JSONObject json, JSONObject extend) {
        Map<String, Object> params = json.getInnerMap();
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/user/organizationReapply", null, params, true);
        log.info("organizationReapply 请求结果:" + str);
        return str;
    }

    // --------------发起签约------------------------
    // 本地文件发起,html源码发起,API模板发起, 参数不一样
    public String applySign(JSONObject json, JSONObject extend) {
        Map<String, Object> params = json.getInnerMap();
        //params.put("notifyUrl", properties.getNotifyUrl());
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/sign/applySign", null, params, true);
        log.info("applySign 请求结果:" + str);
        return str;
    }

    // --------------其他签约------------------------
    //获取签约链接
    public String link(JSONObject json, JSONObject extend) {
        Map<String, Object> params = json.getInnerMap();
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/sign/link", null, params, true);
        log.info("applySign 请求结果:" + str);
        return str;
    }

    //发送签约提醒
    public String notify(JSONObject json, JSONObject extend) {
        Map<String, Object> params = json.getInnerMap();
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/sign/notify", null, params, true);
        log.info("applySign 请求结果:" + str);
        return str;
    }

    //获取签约查看链接
    public String linkAnonyDetail(JSONObject json, JSONObject extend) {
        Map<String, Object> params = json.getInnerMap();
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/sign/linkAnonyDetail", null, params, true);
        log.info("linkAnonyDetail 请求结果:" + str);
        return str;
    }

    //获取文件下载链接
    public String linkFile(JSONObject json, JSONObject extend) {
        Map<String, Object> params = json.getInnerMap();
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/sign/linkFile", null, params, true);
        log.info("linkFile 请求结果:" + str);
        return str;
    }

    //撤销签约合同
    public String cancel(JSONObject json, JSONObject extend) {
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        Map<String, Object> params = json.getInnerMap();
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/sign/cancel", null, params, true);
        log.info("cancel 请求结果:" + str);
        return str;
    }

    //签约状态查询
    public String status(JSONObject json, JSONObject extend) {
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        Map<String, Object> params = json.getInnerMap();
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        String str = HttpClientUtils.init().getPost(properties.getServicesUrl() + "/v2/sign/status", null, params, true);
        log.info("cancel 请求结果:" + str);
        return str;
    }

    //二要素校验
    public String userValid(JSONObject json, JSONObject extend) {
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        RequestUtils requestUtils = RequestUtils.init(properties.getServicesUrl(), properties.getAppKey(), properties.getAppSecret());
        Map<String, Object> params = json.getInnerMap();
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        ResultInfo<Void> ri = requestUtils.doPost("/v2/auth/userValid", params);
        log.info("userValid 请求结果:" + ri);
        return JSONObject.toJSONString(ri);
    }

    //企业工商四要素核验
    public String entFourVerify(JSONObject json, JSONObject extend) {
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        RequestUtils requestUtils = RequestUtils.init(properties.getServicesUrl(), properties.getAppKey(), properties.getAppSecret());
        Map<String, Object> params = json.getInnerMap();
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        ResultInfo<Void> ri = requestUtils.doPost("/v2/auth/entFourVerify", params);
        log.info("entFourVerify 请求结果:" + ri);
        return JSONObject.toJSONString(ri);
    }

    //企业基本信息核验(企业工商三要素核验)
    public String enterpriseInfoMatch(JSONObject json, JSONObject extend) {
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        RequestUtils requestUtils = RequestUtils.init(properties.getServicesUrl(), properties.getAppKey(), properties.getAppSecret());
        Map<String, Object> params = json.getInnerMap();
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        ResultInfo<Void> ri = requestUtils.doPost("/v2/auth/enterpriseInfoMatch", params);
        log.info("enterpriseInfoMatch 请求结果:" + ri);
        return JSONObject.toJSONString(ri);
    }

    //个人证书申请资料上传
    public String cloudCertiPerInfo(JSONObject json, JSONObject extend) {
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        RequestUtils requestUtils = RequestUtils.init(properties.getServicesUrl(), properties.getAppKey(), properties.getAppSecret());
        Map<String, Object> params = json.getInnerMap();
        JunziqianUtils.fillSign(properties.getAppKey(), properties.getAppSecret(), params);
        ResultInfo<Void> ri = requestUtils.doPost("/v2/user/cloudCertiPerInfo", params);
        log.info("cloudCertiPerInfo 请求结果:" + ri);
        return JSONObject.toJSONString(ri);
    }

    //验签
    public String verifySign(JSONObject json, JSONObject extend) {
        JunziqianProperties properties = junziqianConfig.getConfigMap().get(extend.getString("companyId"));
        String verifySign = sha1Hex("data" + json.getString("data") + "method" + json.getString("method") + "version" + json.getString("version") + "timestamp" + json.getString("timestamp") + "appKey" + properties.getAppKey() + "appSecret" + properties.getAppSecret());
        String sign = json.getString("sign");
        return sign.equals(verifySign) ? "success" : "fail";
    }

    @Override
    public String getChannel() {
        return SignatureChannelEnum.JUNZIQIAN.getCode();
    }
}
