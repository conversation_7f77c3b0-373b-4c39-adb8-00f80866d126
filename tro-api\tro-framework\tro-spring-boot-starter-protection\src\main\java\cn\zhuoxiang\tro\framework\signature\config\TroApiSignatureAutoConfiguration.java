package cn.zhuoxiang.tro.framework.signature.config;

import cn.zhuoxiang.tro.framework.redis.config.TroRedisAutoConfiguration;
import cn.zhuoxiang.tro.framework.signature.core.aop.ApiSignatureAspect;
import cn.zhuoxiang.tro.framework.signature.core.redis.ApiSignatureRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * HTTP API 签名的自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(after = TroRedisAutoConfiguration.class)
public class TroApiSignatureAutoConfiguration {

    @Bean
    public ApiSignatureAspect signatureAspect(ApiSignatureRedisDAO signatureRedisDAO) {
        return new ApiSignatureAspect(signatureRedisDAO);
    }

    @Bean
    public ApiSignatureRedisDAO signatureRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new ApiSignatureRedisDAO(stringRedisTemplate);
    }

}
