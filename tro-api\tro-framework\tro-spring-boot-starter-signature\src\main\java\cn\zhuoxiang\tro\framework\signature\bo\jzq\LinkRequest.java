package cn.zhuoxiang.tro.framework.signature.bo.jzq;

import lombok.Data;

/**
 * 获取签约链接请求参数
 */
@Data
public class LinkRequest {
    /**
     * 合同编号（合同发起接口中生成的APL开头的编号）。
     * 必填项。
     */
    private String applyNo;

    /**
     * 签约人名称（合同发起接口中签署人姓名）。
     * 必填项。
     */
    private String fullName;

    /**
     * 签约人证件号（合同发起接口中签署人证件号）。
     * 必填项。
     */
    private String identityCard;

    /**
     * 证件类型：1身份证, 2护照, 3台胞证, 4港澳居民来往内地通行证,
     * 11营业执照, 12统一社会信用代码, 20子账号, 99其他。
     * 必填项。
     */
    private Integer identityType;

    /**
     * PC端合同签署后跳转到查看详情页隐藏两边信息。
     * 可选项，默认为不设置。
     * 1表示隐藏两边信息。
     */
    private Integer viewMode;

    /**
     * 使用自定义域名，嵌入小程序推荐设置该参数。
     * 可选项，默认为不设置。
     * 1表示使用自定义域名。
     */
    private Integer useCustomDomain;
}
