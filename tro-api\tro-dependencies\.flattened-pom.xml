<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cn.zhuoxiang.boot</groupId>
  <artifactId>tro-dependencies</artifactId>
  <version>2.2.0-snapshot</version>
  <packaging>pom</packaging>
  <name>${project.artifactId}</name>
  <description>基础 bom 文件，管理整个项目的依赖版本</description>
  <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>
  <properties>
    <tika-core.version>2.9.2</tika-core.version>
    <podam.version>8.0.2.RELEASE</podam.version>
    <captcha-plus.version>2.0.3</captcha-plus.version>
    <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
    <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
    <opengauss.jdbc.version>5.0.2</opengauss.jdbc.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <fastjson.version>1.2.83</fastjson.version>
    <weixin-java.version>4.6.0</weixin-java.version>
    <mybatis-plus-generator.version>3.5.7</mybatis-plus-generator.version>
    <itextpdf.version>********</itextpdf.version>
    <mybatis.version>3.5.16</mybatis.version>
    <opentracing.version>0.33.0</opentracing.version>
    <mybatis-plus-join.version>1.4.13</mybatis-plus-join.version>
    <kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
    <hutool-5.version>5.8.29</hutool-5.version>
    <revision>2.2.0-snapshot</revision>
    <jsch.version>0.1.55</jsch.version>
    <xercesImpl.version>2.12.2</xercesImpl.version>
    <rocketmq-spring.version>2.3.0</rocketmq-spring.version>
    <ip2region.version>2.7.0</ip2region.version>
    <okio.version>3.5.0</okio.version>
    <itextpdf-kernel.version>8.0.5</itextpdf-kernel.version>
    <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
    <redisson.version>3.32.0</redisson.version>
    <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
    <spring-boot-admin.version>3.3.2</spring-boot-admin.version>
    <minio.version>8.5.7</minio.version>
    <jedis-mock.version>1.1.2</jedis-mock.version>
    <spring.boot.version>3.3.1</spring.boot.version>
    <guava.version>33.2.1-jre</guava.version>
    <itextpdf-asian.version>5.2.0</itextpdf-asian.version>
    <springdoc.version>2.3.0</springdoc.version>
    <poi.version>4.1.2</poi.version>
    <lock4j.version>2.2.7</lock4j.version>
    <commons-io.version>2.15.1</commons-io.version>
    <pdfbox.version>3.0.3</pdfbox.version>
    <hutool-6.version>6.0.0-M14</hutool-6.version>
    <okhttp3.version>4.11.0</okhttp3.version>
    <commons-net.version>3.11.1</commons-net.version>
    <jsoup.version>1.18.1</jsoup.version>
    <mybatis-plus.version>3.5.7</mybatis-plus.version>
    <jimureport.version>1.7.8</jimureport.version>
    <knife4j.version>4.5.0</knife4j.version>
    <lombok.version>1.18.34</lombok.version>
    <easyexcel.verion>3.3.4</easyexcel.verion>
    <flowable.version>7.0.1</flowable.version>
    <druid.version>1.2.23</druid.version>
    <easy-trans.version>3.0.5</easy-trans.version>
    <skywalking.version>9.0.0</skywalking.version>
    <mockito-inline.version>5.2.0</mockito-inline.version>
    <justauth.version>2.0.5</justauth.version>
    <dm8.jdbc.version>8.1.3.62</dm8.jdbc.version>
    <velocity.version>2.3</velocity.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring.boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.github.mouzt</groupId>
        <artifactId>bizlog-sdk</artifactId>
        <version>${bizlog-sdk.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-biz-tenant</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-biz-data-permission</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-biz-ip</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-web</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-security</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-websocket</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-mybatis</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-3-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>${mybatis.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis-plus-generator.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        <version>${dynamic-datasource.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.yulichang</groupId>
        <artifactId>mybatis-plus-join-boot-starter</artifactId>
        <version>${mybatis-plus-join.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-spring-boot-starter</artifactId>
        <version>${easy-trans.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-mybatis-plus-extend</artifactId>
        <version>${easy-trans.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-anno</artifactId>
        <version>${easy-trans.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-redis</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>${redisson.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.dameng</groupId>
        <artifactId>DmJdbcDriver18</artifactId>
        <version>${dm8.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.opengauss</groupId>
        <artifactId>opengauss-jdbc</artifactId>
        <version>${opengauss.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.kingbase</groupId>
        <artifactId>kingbase8</artifactId>
        <version>${kingbase.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-job</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-mq</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>${rocketmq-spring.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-protection</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        <version>${lock4j.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-monitor</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-trace</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-logback-1.x</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-opentracing</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-api</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-util</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-noop</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-server</artifactId>
        <version>${spring-boot-admin.version}</version>
        <exclusions>
          <exclusion>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-server-cloud</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-client</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-test</artifactId>
        <version>2.2.0-snapshot</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>${mockito-inline.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring.boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.fppt</groupId>
        <artifactId>jedis-mock</artifactId>
        <version>${jedis-mock.version}</version>
      </dependency>
      <dependency>
        <groupId>uk.co.jemos.podam</groupId>
        <artifactId>podam</artifactId>
        <version>${podam.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-common</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.zhuoxiang.boot</groupId>
        <artifactId>tro-spring-boot-starter-excel</artifactId>
        <version>2.2.0-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-jdk8</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool-5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara.hutool</groupId>
        <artifactId>hutool-extra</artifactId>
        <version>${hutool-6.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>${easyexcel.verion}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tika</groupId>
        <artifactId>tika-core</artifactId>
        <version>${tika-core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>${transmittable-thread-local.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-net</groupId>
        <artifactId>commons-net</artifactId>
        <version>${commons-net.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jsch</artifactId>
        <version>${jsch.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xingyuv</groupId>
        <artifactId>spring-boot-starter-captcha-plus</artifactId>
        <version>${captcha-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lionsoul</groupId>
        <artifactId>ip2region</artifactId>
        <version>${ip2region.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>${jsoup.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okio</groupId>
        <artifactId>okio</artifactId>
        <version>${okio.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp3.version}</version>
      </dependency>
      <dependency>
        <groupId>io.minio</groupId>
        <artifactId>minio</artifactId>
        <version>${minio.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xingyuv</groupId>
        <artifactId>spring-boot-starter-justauth</artifactId>
        <version>${justauth.version}</version>
        <exclusions>
          <exclusion>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>weixin-java-pay</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-mp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>xerces</groupId>
        <artifactId>xercesImpl</artifactId>
        <version>${xercesImpl.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.validation</groupId>
        <artifactId>jakarta.validation-api</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>fontbox</artifactId>
        <version>${pdfbox.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pdfbox</groupId>
        <artifactId>pdfbox</artifactId>
        <version>${pdfbox.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>${poi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-ooxml</artifactId>
        <version>${poi.version}</version>
      </dependency>
      <dependency>
        <groupId>com.itextpdf</groupId>
        <artifactId>kernel</artifactId>
        <version>${itextpdf-kernel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.itextpdf</groupId>
        <artifactId>itextpdf</artifactId>
        <version>${itextpdf.version}</version>
      </dependency>
      <dependency>
        <groupId>com.itextpdf</groupId>
        <artifactId>itext-asian</artifactId>
        <version>${itextpdf-asian.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten-maven-plugin.version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
          <updatePomFile>true</updatePomFile>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
