package cn.zhuoxiang.tro.framework.signature.enums.jzq;

import java.util.HashMap;
import java.util.Map;

/**
 * ErrorCode 枚举表示不同的错误码和对应的描述。
 */
public enum JzqErrorCodeEnum {
    NO_REACH("NO_REACH", "客户端请求不到服务端。"),
    NO_RESULT("NO_RESULT", "服务端无返回。"),
    BUSINESS_ERROR("BUSINESS_ERROR", "业务错误。一般是还没有分类业务错误"),
    SERVICE_INTERNAL_ERROR("SERVICE_INTERNAL_ERROR", "系统内部错误。服务端还没有捕获的异常"),
    PERMISSION_CLOSE("PERMISSION_CLOSE", "操作此业务被关闭"),
    PERMISSION_DENIED("PERMISSION_DENIED", "没有权限操作此接口"),
    LOCK_EXCEPTION("LOCK_EXCEPTION", "锁定异常"),
    NOT_INIT("NOT_INIT", "业务还在处理中"),
    REPEAT_DEAL("REPEAT_DEAL", "业务不能重复处理"),
    DFS_ERROR("DFS_ERROR", "文件处理失败"),
    ACCOUNT_CONFLICT("ACCOUNT_CONFLICT", "账号冲突"),
    ACCOUNT_NOT_EXISTS("ACCOUNT_NOT_EXISTS", "账号不存在"),
    BUSINESSNO_CONFLICT("BUSINESSNO_CONFLICT", "营业执照号冲突"),
    IDEN_AUTH_ERROR("IDEN_AUTH_ERROR", "身份认证未成功"),
    USER_TYPE_CONFLICT("USER_TYPE_CONFLICT", "用户类型冲突"),
    ACCOUNT_BALANCE_INSUFFICIENT("ACCOUNT_BALANCE_INSUFFICIENT", "账户余额不足"),
    TEMPLATE_NOT_EXISTS("TEMPLATE_NOT_EXISTS", "没有配置此模版"),
    SMS_CODE_NOT_EQUAL("SMS_CODE_NOT_EQUAL", "验证码不相同"),
    SMS_CODE_NOT_EXISTS("SMS_CODE_NOT_EXISTS", "验证码不存在"),
    SERV_CERT_NOT_APPLY("SERV_CERT_NOT_APPLY", "还没有申请过云证书"),
    CERT_NOT_APPLY("CERT_NOT_APPLY", "还没有申请过证书"),
    INSURE_TIMES_OVER("INSURE_TIMES_OVER", "购买保险次数已经用完"),
    FACE_TIMES_OVER("FACE_TIMES_OVER", "购买人脸验证次数已经用完"),
    FACE_VIDEO_NOT_EXISTS("FACE_VIDEO_NOT_EXISTS", "人脸视频结果还不存在"),
    CONTRACT_NOT_EXISTS("CONTRACT_NOT_EXISTS", "合同不存在"),
    SIGN_INFO_NOT_EXISTS("SIGN_INFO_NOT_EXISTS", "签约方不存在"),
    SIGN_STATUS_ERROR("SIGN_STATUS_ERROR", "合同签约状态不正确"),
    DEAL_TYPE_ERROR("DEAL_TYPE_ERROR", "处理类型不正确"),
    HTTP_SIGN_TIMEOUT("HTTP_SIGN_TIMEOUT", "参数签名不正确"),
    HTTP_PARAM_ERROR("HTTP_PARAM_ERROR", "参数不符合规范。参数不满足设置范围时会有此错误"),
    APPKEY_INACTIVE("APPKEY_INACTIVE", "AppKey未激活");

    private final String code;
    private final String description;

    // 使用静态映射表来查找描述
    private static final Map<String, JzqErrorCodeEnum> CODE_TO_ERROR_MAP = new HashMap<>();

    static {
        for (JzqErrorCodeEnum errorCode : values()) {
            CODE_TO_ERROR_MAP.put(errorCode.getCode(), errorCode);
        }
    }

    JzqErrorCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据错误码获取对应的描述。
     *
     * @param code 错误码
     * @return 错误描述，如果未找到对应的错误码则返回 null
     */
    public static String getDesc(String code) {
        JzqErrorCodeEnum errorCode = CODE_TO_ERROR_MAP.get(code);
        return errorCode != null ? errorCode.getDescription() : null;
    }

    @Override
    public String toString() {
        return "ErrorCode{" +
                "code='" + code + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
