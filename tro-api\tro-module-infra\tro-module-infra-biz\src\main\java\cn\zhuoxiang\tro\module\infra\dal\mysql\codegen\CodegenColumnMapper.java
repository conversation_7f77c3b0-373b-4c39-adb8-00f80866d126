package cn.zhuoxiang.tro.module.infra.dal.mysql.codegen;

import cn.zhuoxiang.tro.framework.mybatis.core.mapper.BaseMapperX;
import cn.zhuoxiang.tro.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.zhuoxiang.tro.module.infra.dal.dataobject.codegen.CodegenColumnDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CodegenColumnMapper extends BaseMapperX<CodegenColumnDO> {

    default List<CodegenColumnDO> selectListByTableId(Long tableId) {
        return selectList(new LambdaQueryWrapperX<CodegenColumnDO>()
                .eq(CodegenColumnDO::getTableId, tableId)
                .orderByAsc(CodegenColumnDO::getId));
    }

    default void deleteListByTableId(Long tableId) {
        delete(new LambdaQueryWrapperX<CodegenColumnDO>()
                .eq(CodegenColumnDO::getTableId, tableId));
    }

}
