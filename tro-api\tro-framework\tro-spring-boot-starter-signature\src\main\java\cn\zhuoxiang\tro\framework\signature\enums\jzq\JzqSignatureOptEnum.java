package cn.zhuoxiang.tro.framework.signature.enums.jzq;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 签约渠道的编码的枚举
 */
@Getter
@AllArgsConstructor
public enum JzqSignatureOptEnum {
    organizationAuditStatus("organizationAuditStatus", "企业实名认证状态查询"),
    organizationCreate("organizationCreate", "企业实名认证上传"),
    organizationReapply("organizationReapply", "企业实名认证重传"),
    applySign("applySign", "发起签约"),
    link("link", "获取签约链接"),
    notify("notify", "发送签约提醒"),
    linkAnonyDetail("linkAnonyDetail", "获取签约查看链接"),
    linkFile("linkFile", "获取文件下载链接"),
    status("status", "签约状态查询"),
    userValid("userValid", "二要素校验"),
    entFourVerify("entFourVerify", "企业工商四要素核验"),
    enterpriseInfoMatch("enterpriseInfoMatch", "企业基本信息核验(企业工商三要素核验)"),
    verifySign("verifySign", "回调验签"),
    cancel("cancel", "撤销签约合同");

    /**
     * 编码
     */
    private final String code;
    /**
     * 名字
     */
    private final String name;


    public static JzqSignatureOptEnum getByCode(String code) {
        return ArrayUtil.firstMatch(o -> o.getCode().equals(code), values());
    }
}
