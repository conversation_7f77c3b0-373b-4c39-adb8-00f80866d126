package cn.zhuoxiang.tro.framework.signature.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 签约渠道的编码的枚举
 */
@Getter
@AllArgsConstructor
public enum SignatureChannelEnum {

    JUNZIQIAN("jun<PERSON><PERSON>an", "君子签"),
    EQIANBAO("wallet", "e签宝");

    /**
     * 编码
     */
    private final String code;
    /**
     * 名字
     */
    private final String name;


    public static SignatureChannelEnum getByCode(String code) {
        return ArrayUtil.firstMatch(o -> o.getCode().equals(code), values());
    }
}
