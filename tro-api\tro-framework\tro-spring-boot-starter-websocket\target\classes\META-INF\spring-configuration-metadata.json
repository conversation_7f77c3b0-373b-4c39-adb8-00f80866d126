{"groups": [{"name": "tro.websocket", "type": "cn.zhuoxiang.tro.framework.websocket.config.WebSocketProperties", "sourceType": "cn.zhuoxiang.tro.framework.websocket.config.WebSocketProperties"}], "properties": [{"name": "tro.websocket.path", "type": "java.lang.String", "description": "WebSocket 的连接路径", "sourceType": "cn.zhuoxiang.tro.framework.websocket.config.WebSocketProperties"}, {"name": "tro.websocket.sender-type", "type": "java.lang.String", "description": "消息发送器的类型 可选值：local、redis、rocketmq、kafka、rabbitmq", "sourceType": "cn.zhuoxiang.tro.framework.websocket.config.WebSocketProperties"}], "hints": []}