<template>
	<view class="page_detail sub-page-full-width desktop-tabbar-layout">
		<top-navbar title="侵权体检详情" :autoBack="true" > </top-navbar>
		<content-wapper>
			<view class="text_row">
				<text class="label">体检时间：</text>
				<text class="value">{{info.checkDate}}</text>
			</view>
			<view class="text_row">
				<text class="label">体检风险等级：</text>
				<text class="value" :style="{color:info.riskColor}">{{info.riskLevel}}</text>
			</view>
			<template v-if="info.checkResultList?.length>0">
				<view class="label"> 体检结果说明: </view>
				<view class="result_content" v-for="(item,index) in info.checkResultList" :key="index">
					<!-- Type -->
					<view class="info_row" v-if="getDisplayType(item)">
						<text class="info_label">Type:</text>
						<text class="info_value">{{getDisplayType(item)}}</text>
					</view>

					<!-- IP Owner -->
					<view class="info_row" v-if="item.ipOwner">
						<text class="info_label">IP Owner:</text>
						<text class="info_value">{{item.ipOwner}}</text>
					</view>

					<!-- Registration Number -->
					<view class="info_row">
						<text class="info_label">Registration Number:</text>
						<text class="info_value">{{getDisplayRegNo(item)}}</text>
					</view>

					<!-- Risk Level (from parent info) -->
					<view class="info_row">
						<text class="info_label">Risk Level:</text>
						<text class="info_value" :style="{color:info.riskColor}">{{info.riskLevel}}</text>
					</view>

					<!-- Risk Description -->
					<view class="info_row" v-if="item.riskDescription">
						<text class="info_label">Risk Description:</text>
						<text class="info_value">{{item.riskDescription}}</text>
					</view>

					<!-- Has been used in TRO -->
					<view class="info_row">
						<text class="info_label">Has been used in TRO:</text>
						<text class="info_value">{{hasBeenUsedInTro(item) ? 'Yes' : 'No'}}</text>
					</view>

					<!-- TRO Case Details (only show if has been used in TRO) -->
					<template v-if="hasBeenUsedInTro(item)">
						<view class="info_row" v-if="item.lastCaseDocket">
							<text class="info_label">Last Docket number:</text>
							<text class="info_value">{{item.lastCaseDocket}}</text>
						</view>
						<view class="info_row" v-if="item.plaintiffName">
							<text class="info_label">Plaintiff Name:</text>
							<text class="info_value">{{item.plaintiffName}}</text>
						</view>
						<view class="info_row" v-if="item.numberOfCases">
							<text class="info_label">Number of cases:</text>
							<text class="info_value">{{item.numberOfCases}}</text>
						</view>
					</template>

					<!-- IP Asset Images (handle both old and new formats) -->
					<view class="list_wapper" v-if="getDisplayIpAssetUrls(item).length > 0">
						<view class="info_label">图片信息：</view>
						<view class="list_row">
							<view class="list_box" v-for="(img, imgIndex) in getDisplayIpAssetUrls(item)" :key="imgIndex">
								<view class="box_content">
									<view class="box_img">
										<view class="img_view">
											<up-lazy-load
											threshold="450"
											height="416rpx"
											width="236rpx"
											:image="getImgPath(info.id,'results',img,true,true)"
											:index="imgIndex"
											border-radius="10"
											@click="previewImg(getImgPath(info.id,'results',img,true,true))"></up-lazy-load>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- Report Details -->
					<view class="report_content" v-if="item.report">
						<view class="info_label" @click="item.showReport = !item.showReport">
							<text>报告详情：</text>
							<text :style="{color: item.showReport ? '#FE7900' : '#666'}">{{ item.showReport ? '收起' : '展开' }}</text>
						</view>
						<view class="report_markdown" v-if="item.showReport">
							<u-parse :content="formatMarkdown(item.report)" :tag-style="tagStyle" />
						</view>
					</view>
				</view>
			</template>
		</content-wapper>
		<br><br>
		<content-wapper>
			<view class="result_content">
				<view class="info_row" v-if="info.productCategory">
					<text class="label">产品类别：</text>
					<text class="value">{{ info.productCategory }}</text>
				</view>
				<view class="info_row" v-if="info.keyword">
					<text class="label">Keywords:</text>
					<text class="value">{{ info.keyword }}</text>
				</view>
				<view class="list_wapper" v-if="info.images">
					<view class="info_row">您使用的知识产权（图片）：</view>
					<view class="list_row">
						<view class="list_box" v-for="(img, index) in str2arr(info.images)" :key="index">
							<view class="box_content">
								<view class="box_img">
									<view class="img_view">
										<up-lazy-load
										threshold="450" 
										height="416rpx"
										width="236rpx"
										:image=img.url
										:index="index" 
										border-radius="10"
										@click="previewImg(img.url)"></up-lazy-load>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="info_row" v-if="info.productDescribe">
					<text class="label">产品描述：</text>
					<text class="value">{{ info.productDescribe }}</text>
				</view>

				<view class="list_wapper" v-if="info.productImages">
					<view class="info_row">您的产品图片（需含有知识产权）：</view>
					<view class="list_row">
						<view class="list_box" v-for="(img, index) in str2arr(info.productImages)" :key="index">
							<view class="box_content">
								<view class="box_img">
									<view class="img_view">
										<up-lazy-load
										threshold="450" 
										height="416rpx"
										width="236rpx"
										:image=img.url
										:index="index" 
										border-radius="10"
										@click="previewImg(img.url)"></up-lazy-load>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="list_wapper" v-if="info.otherProductImages">
					<view class="info_row">其他产品图片：</view>
					<view class="list_row">
						<view class="list_box" v-for="(img, index) in str2arr(info.otherProductImages)" :key="index">
							<view class="box_content">
								<view class="box_img">
									<view class="img_view">
										<up-lazy-load
										threshold="450" 
										height="416rpx"
										width="236rpx"
										:image=img.url
										:index="index" 
										border-radius="10"
										@click="previewImg(img.url)"></up-lazy-load>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="info_row" v-if="info.referenceSource">
					<text class="label">Reference Source:</text>
					<text class="value">{{ info.referenceSource }}</text>
				</view>
				<view class="list_wapper" v-if="info.referenceSource">
					<view class="info_row">其他产品图片：</view>
					<view class="list_row">
						<view class="list_box" v-for="(img, index) in str2arr(info.referenceSource)" :key="index">
							<view class="box_content">
								<view class="box_img">
									<view class="img_view">
										<up-lazy-load
										threshold="450" 
										height="416rpx"
										width="236rpx"
										:image=img.url
										:index="index" 
										border-radius="10"
										@click="previewImg(img.url)"></up-lazy-load>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="recheck_box">
					<view class="btn" @click="checkAgain">重新体检</view>
				</view>
				<view class="tips_row">
					<text>剩余</text>
					<text class="gray_text">{{ userInfo?.checkedCount || 0 }}</text>
					<text>次体检机会</text>
					<text class="color_text" @click="navTo('/pagesChecked/chooseService/index')">购买体检次数</text>
				</view>
			</view>
		</content-wapper>
		
		
		<my-model
			v-model="tipsShow" 
			btnAlign="vertical"
			submitText="去购买"
			:icon="config.imgPath + '/model_icon.png'" 
			@confirm="handleConfirm">
			<text>{{modelContent}}</text>
		</my-model>
		
		<page-bottom v-if="info.riskLevel=='高风险'||info.riskLevel=='中风险'">
			<button open-type="contact" class="handle_btn">结果咨询</button>
		</page-bottom>
	</view>

	<!-- Add desktop tabbar component -->
	<desktop-tabbar></desktop-tabbar>
</template>

<script setup>
	import { onLoad,onShow } from '@dcloudio/uni-app'
	import { computed, ref } from 'vue';
	import { checkAgainApi, detailApi } from '@/api/checked'
  import { subCountApi } from '@/api/my';
	import { useUserStore } from '@/store/modules/userStore';
	import { schemas } from './data'
	import { dicts, getDictColor } from '@/utils/dict';
	import { str2arr, getImgPath } from '@/utils';
	import config from '@/common/config';
	const  userStore =  useUserStore()
	
	const userInfo = computed(()=>userStore.getUserInfo)

	const id = ref('')
	onLoad((o)=>{
		id.value = o.id
		getDetail()
	})
	onShow(()=>{
		userStore.setUserInfo()
	})
	
	const info = ref({})
	async function getDetail(){
		const res = await detailApi({id:id.value})
		console.log(res)
		res.riskColor = getDictColor(dicts.riskList,res.riskLevel)
		res.checkResultList?.forEach(item => {
			item.showReport = false
		})
		info.value = res
	}
	
	const previewImg = (img)=>{
		uni.previewImage({
			current:0,
			urls:[img]
		})
	}
	
	//再次体检
	const loading = ref(false)
	const tipsShow = ref(false)
	const modelContent = ref('')
	const checkAgain = async ()=>{
		if(loading.value) return
		if(userInfo.value.checkedCount<=0){
			tipsShow.value = true
			modelContent.value = '核查次数不足，是否购买核查次数？'
			return
		}
		uni.requestSubscribeMessage({
			tmplIds:['j3A_C3KFWW1mcuP6SpVMilE0NaCXsY1RoMw4OyhpH_0'],
			complete: async (e)=>{
				try{
					loading.value = true
					await checkAgainApi(info.value)
          subCountApi().then(() => {
            userStore.setUserInfo()
          })
					uni.$u.toast('信息提交成功')
					setTimeout(()=>{
						uni.navigateBack()
					},1500)
				}catch(e){
					if(e.code=='1_001_001_008'){
						tipsShow.value = true
						modelContent.value = '核查次数不足，是否购买核查次数？'
					}else{
						uni.$u.toast(e.msg)
					}
					loading.value = false
					setStorage('check_form_data',formData)
				}
			},
		})
	}
	//购买次数
	const handleConfirm = ()=>{
		uni.navigateTo({
			url:'/pagesChecked/chooseService/index'
		})
		tipsShow.value = false
	}
	
	// Helper functions for new result format display
	const getDisplayType = (item) => {
		// Prioritize new format (ipType) over old format (type)
		return item.ipType || item.type || '';
	}

	const getDisplayRegNo = (item) => {
		if (item.regNo && item.regNo !== 'None') {
			return item.regNo;
		}
		return 'None';
	}

	const hasBeenUsedInTro = (item) => {
		return item.plaintiffId != null;
	}

	const getDisplayIpAssetUrls = (item) => {
		// Prioritize new format (ipAssetUrls) over old format (ipImage)
		if (item.ipAssetUrls && item.ipAssetUrls.length > 0) {
			return item.ipAssetUrls;
		}
		// Fallback to old format
		if (item.ipImage) {
			return str2arr(item.ipImage);
		}
		return [];
	}

	const formatMarkdown = (content) => {
		if (!content) return '';

		// Replace double asterisks with HTML bold tags
		content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

		// Convert single line breaks to <br> tags
		content = content.replace(/\n/g, '<br>');

		// Add proper spacing after bullet points
		content = content.replace(/\*(.*?)\n/g, '• $1<br>');

		// Add proper spacing for numbered lists
		content = content.replace(/(\d+)\.(.*?)\n/g, '$1.$2<br>');

		return content;
	}
	
	const tagStyle = ref({
		p: 'line-height: 1.8em; font-size: 28rpx; color: #333; margin-bottom: 20rpx; text-align: justify;',
		h1: 'font-size: 36rpx; font-weight: bold; margin: 30rpx 0 20rpx 0; color: #222;',
		h2: 'font-size: 32rpx; font-weight: bold; margin: 25rpx 0 15rpx 0; color: #333;',
		h3: 'font-size: 30rpx; font-weight: bold; margin: 20rpx 0 12rpx 0; color: #444;',
		strong: 'font-weight: bold; color: #333;',
		em: 'font-style: italic;',
		ul: 'list-style-type: disc; padding-left: 40rpx; margin: 15rpx 0;',
		ol: 'list-style-type: decimal; padding-left: 40rpx; margin: 15rpx 0;',
		li: 'margin-bottom: 10rpx; font-size: 28rpx; color: #333;',
		br: 'display: block; margin: 12rpx 0;',
	})
	
</script>

<style lang="scss">
	.page_detail{
		padding-bottom: 40rpx;
		.info_content{
			background: white;
			margin-top: 20rpx;
			padding-bottom: 20rpx;
		}
		.label,.value{
			font-family: PingFangSC-Regular;
			font-weight: 600;
			font-size: 32rpx;
			color: #333333;
		}
		.text_row{
			margin-bottom: 40rpx;
		}
		
		
		.result_content{
			border: 1px solid rgba(229, 229, 229, 1);
			border-radius: 20rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;
			margin-top: 20rpx;
			&:last-child{
				margin-bottom: 0;
			}
			.info_row{
				display: flex;
				font-size: 28rpx;
				margin-top: 20rpx;
				&:first-child{
					margin-top: 0;
				}
				.info_label{
					flex-shrink: 0;
				}
				.info_value{
					color: #666;
				}
			}
			.list_wapper{
				// background: white;
				padding-bottom: 60rpx;
				margin-top: 20rpx;
				padding-top: 20rpx;
				.list_row{
					// #ifdef H5
					display: grid;
					grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
					gap: 20rpx;
					padding: 20rpx;
					// #endif
					// #ifndef H5
					padding: 20rpx;
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;
					// #endif
					.list_box{
						// #ifdef H5
						break-inside: avoid;
						// #endif
						// #ifndef H5
						padding: 10rpx ;
						width: calc(50% - 20rpx);
						// #endif
						display: flex;
						//两列高度一致
						align-items: stretch;
						.box_content{
							border: 1px solid #E5E5E5;
							border-radius: 15rpx;
							padding: 16rpx;
							width: 100%;
							display: flex;
							flex-direction: column;
							align-items: center;
							.box_img{
								margin-bottom: 20rpx;
								width: 100%;
								height: 100%;
								display: flex;
								align-items: center;
								.img_view{
									width: 100%;
								}
							}
							.text_content{
								width: 100%;
								text-align: left;
								.text_row{
									color: #666666;
									font-size: 28rpx;
									font-family: PingFangSC;
									font-weight: 400;
									line-height: 48rpx;
									.label{
										font-size: 28rpx;
										line-height: 48rpx;
									}
									.blue{
										color: #498BFE;
										text-decoration: underline;
									}
									text{
										word-break: break-all;
									}
								}
								
							}
						}
					}
				}
			}
			.report_content {
				margin-top: 20rpx;
				.report_markdown {
					margin-top: 20rpx;
					padding: 20rpx;
					background: #f8f8f8;
					border-radius: 10rpx;
					
					:deep(strong) {
						font-weight: bold;
						color: #333;
					}
					
					:deep(br) {
						display: block;
						margin: 12rpx 0;
					}
					
					:deep(ul), :deep(ol) {
						padding-left: 40rpx;
						margin: 15rpx 0;
					}
					
					:deep(li) {
						margin-bottom: 10rpx;
					}
				}
			}
		}
		
		.result_title{
			font-family: PingFangSC-Medium;
			font-weight: 500;
			font-size: 32rpx;
			color: #202020;
			
		}

		.recheck_box{
			display: flex;
			justify-content: center;
			margin-top: 40rpx;
			.btn{
				padding: 15rpx 60rpx;
				border-radius: 40rpx;
				color: white;
				background-image: linear-gradient(90deg, #FFB100 1%, #FE7900 100%);
				&:active{
					background-image: linear-gradient(90deg, rgba(255, 212, 56, .7) 1%, rgba(255, 137, 95, .7) 100%);
				}
			}
		}
		.tips_row{
			padding-top: 20rpx;
			text-align: center;
			color: #999999;
			font-family: PingFangSC;
			font-weight: 400;
			font-size: 24rpx;
			.gray_text{
				color: 666;
			}
			.color_text{
				margin-left: 30rpx;
				font-family: PingFangSC-SNaNrpxibold;
				font-weight: 600;
				font-size: 24rpx;
				color: #FE7900;
			}
		}
		.handle_btn{
			height: 100rpx;
			background-image: linear-gradient(90deg, #FFB100 1%, #FE7900 100%);
			border-radius: 50rpx;
			line-height: 100rpx;
			color: white;
		}
	}
</style>