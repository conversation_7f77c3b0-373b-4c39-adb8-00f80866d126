package cn.zhuoxiang.tro.module.tro.controller.app.casecheck.vo;

import cn.zhuoxiang.tro.framework.common.util.date.DateUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class AppCaseCheckResultRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "产品类别", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品类别")
    private String productCategory;

    @Schema(description = "图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图片")
    private String images;

    @Schema(description = "关键字", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("关键字")
    private String keyword;

    @Schema(description = "产品图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品图片")
    private String productImages;

    @Schema(description = "其他产品图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("其他产品图片")
    private String otherProductImages;

    @Schema(description = "产品描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品描述")
    private String productDescribe;

    @Schema(description = "参考来源", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("参考来源")
    private String referenceSource;

    @Schema(description = "参考来源图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("参考来源图片")
    private String referenceImages;

    @Schema(description = "体检时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("体检时间")
    @JsonFormat(pattern = DateUtils.FORMAT_YEAR_MONTH_DAY)
    private LocalDate checkDate;

    @Schema(description = "体检结果")
    @ExcelProperty("体检结果")
    private String result;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "(是否可查看: 0=否，1=是，仅限于高风险报告)")
    private String lockStatus;

    public String getRiskLevel() {
        // 如果 riskLevel 还没有被解析，先解析
        if (riskLevel == null && result != null) {
            parseRiskLevelFromResult();
        }
        return riskLevel;
    }

    private void parseRiskLevelFromResult() {
        if (result != null) {
            try {
                JSONObject resultObject = JSON.parseObject(result);
                riskLevel = resultObject.getString("risk_level");
            } catch (JSONException e) {
                // 处理 JSON 解析异常，例如记录日志或返回默认值
                System.err.println("Error parsing JSON result: " + e.getMessage());
                riskLevel = null;
            }
        }
    }

    @Schema(description = "检查结果明细")
    List<CheckResultVO> checkResultList;

    public List<CheckResultVO> getCheckResultList() {
        if (result != null) {
            try {
                List<CheckResultVO> checkResultLists = new ArrayList<>();
                JSONObject resultObject = JSON.parseObject(result);
                JSONArray checkResult = resultObject.getJSONArray("results");
                for (Object object : checkResult) {
                    CheckResultVO checkResultVO = new CheckResultVO();
                    JSONObject object1 = (JSONObject) object;

                    // Parse both old and new formats
                    parseCheckResultFields(checkResultVO, object1);

                    checkResultLists.add(checkResultVO);
                }
                checkResultList = checkResultLists;
            } catch (JSONException e) {
                // 处理 JSON 解析异常，例如记录日志或返回默认值
                System.err.println("Error parsing JSON result: " + e.getMessage());
                checkResultList = null;
            }
        }
        return checkResultList;
    }

    /**
     * Parse check result fields from JSON object, handling both old and new formats
     */
    private void parseCheckResultFields(CheckResultVO checkResultVO, JSONObject jsonObject) {
        // ========== Handle old format fields ==========
        // Old: "type" -> New: "ip_type"
        String oldType = jsonObject.getString("type");
        String newIpType = jsonObject.getString("ip_type");
        if (newIpType != null) {
            checkResultVO.setIpType(newIpType);
        } else if (oldType != null) {
            // For backward compatibility, set both old and new fields
            checkResultVO.setType(oldType);
            checkResultVO.setIpType(oldType);
        }

        // Common fields that exist in both formats
        checkResultVO.setIpOwner(jsonObject.getString("ip_owner"));
        checkResultVO.setRiskDescription(jsonObject.getString("risk_description"));
        checkResultVO.setReport(jsonObject.getString("report"));

        // ========== Handle image/asset URL fields ==========
        // Old: "ip_image" (string) -> New: "ip_asset_urls" (array)
        Object ipAssetUrls = jsonObject.get("ip_asset_urls");
        Object oldIpImage = jsonObject.get("ip_image");

        if (ipAssetUrls != null) {
            // New format: ip_asset_urls as array
            if (ipAssetUrls instanceof JSONArray) {
                JSONArray urlArray = (JSONArray) ipAssetUrls;
                List<String> urls = new ArrayList<>();
                for (int i = 0; i < urlArray.size(); i++) {
                    urls.add(urlArray.getString(i));
                }
                checkResultVO.setIpAssetUrls(urls);
            }
        } else if (oldIpImage != null) {
            // Old format: ip_image as string
            checkResultVO.setIpImage(jsonObject.getString("ip_image"));
        }

        // ========== Handle text fields ==========
        // Old: "trademark_text" -> New: "text"
        String newText = jsonObject.getString("text");
        String oldTrademarkText = jsonObject.getString("trademark_text");
        if (newText != null) {
            checkResultVO.setText(newText);
        } else if (oldTrademarkText != null) {
            checkResultVO.setText(oldTrademarkText);
        }

        // ========== Handle product URL fields ==========
        // Old: "Query_URL" (array) -> New: "product_url" (string)
        Object newProductUrl = jsonObject.get("product_url");
        Object oldQueryUrl = jsonObject.get("Query_URL");

        if (newProductUrl != null) {
            // New format: product_url as string
            checkResultVO.setProductUrl(jsonObject.getString("product_url"));
        } else if (oldQueryUrl != null) {
            // Old format: Query_URL as array, take first element
            if (oldQueryUrl instanceof JSONArray) {
                JSONArray queryUrlArray = (JSONArray) oldQueryUrl;
                if (queryUrlArray.size() > 0) {
                    checkResultVO.setProductUrl(queryUrlArray.getString(0));
                }
            }
        }

        // ========== Handle plaintiff ID ==========
        // Old: "plaintiff_id" (string) -> New: "plaintiff_id" (integer or null)
        Object plaintiffIdObj = jsonObject.get("plaintiff_id");
        if (plaintiffIdObj != null) {
            if (plaintiffIdObj instanceof Integer) {
                checkResultVO.setPlaintiffId((Integer) plaintiffIdObj);
            } else if (plaintiffIdObj instanceof String) {
                try {
                    checkResultVO.setPlaintiffId(Integer.parseInt((String) plaintiffIdObj));
                } catch (NumberFormatException e) {
                    // If parsing fails, leave as null
                    checkResultVO.setPlaintiffId(null);
                }
            }
        }

        // ========== Handle registration number ==========
        // Old: "reg_no" (array) -> New: "reg_no" (string)
        Object regNoObj = jsonObject.get("reg_no");
        if (regNoObj != null) {
            if (regNoObj instanceof String) {
                // New format: reg_no as string
                checkResultVO.setRegNo((String) regNoObj);
            } else if (regNoObj instanceof JSONArray) {
                // Old format: reg_no as array, join with comma
                JSONArray regNoArray = (JSONArray) regNoObj;
                if (regNoArray.size() > 0) {
                    List<String> regNos = new ArrayList<>();
                    for (int i = 0; i < regNoArray.size(); i++) {
                        regNos.add(regNoArray.getString(i));
                    }
                    checkResultVO.setRegNo(String.join(", ", regNos));
                }
            }
        }

        // ========== Handle new format fields ==========
        checkResultVO.setPlaintiffName(jsonObject.getString("plaintiff_name"));
        checkResultVO.setLastCaseDocket(jsonObject.getString("last_case_docket"));

        // Handle number_of_cases
        Object numberOfCasesObj = jsonObject.get("number_of_cases");
        if (numberOfCasesObj instanceof Integer) {
            checkResultVO.setNumberOfCases((Integer) numberOfCasesObj);
        }

        checkResultVO.setLastCaseDateFiled(jsonObject.getString("last_case_date_filed"));
    }

}
