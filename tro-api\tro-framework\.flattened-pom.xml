<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.zhuoxiang.boot</groupId>
    <artifactId>tro</artifactId>
    <version>2.2.0-snapshot</version>
  </parent>
  <artifactId>tro-framework</artifactId>
  <version>2.2.0-snapshot</version>
  <packaging>pom</packaging>
  <description>该包是技术组件，每个子包，代表一个组件。每个组件包括两部分：
            1. core 包：是该组件的核心封装
            2. config 包：是该组件基于 Spring 的配置

        技术组件，也分成两类：
            1. 框架组件：和我们熟悉的 MyBatis、Redis 等等的拓展
            2. 业务组件：和业务相关的组件的封装，例如说数据字典、操作日志等等。
        如果是业务组件，Maven 名字会包含 biz</description>
  <url>****************:g-cmku3081/caishuipingtai/tro-biz-api.git</url>
  <modules>
    <module>tro-common</module>
    <module>tro-spring-boot-starter-mybatis</module>
    <module>tro-spring-boot-starter-redis</module>
    <module>tro-spring-boot-starter-web</module>
    <module>tro-spring-boot-starter-security</module>
    <module>tro-spring-boot-starter-websocket</module>
    <module>tro-spring-boot-starter-monitor</module>
    <module>tro-spring-boot-starter-protection</module>
    <module>tro-spring-boot-starter-job</module>
    <module>tro-spring-boot-starter-mq</module>
    <module>tro-spring-boot-starter-excel</module>
    <module>tro-spring-boot-starter-test</module>
    <module>tro-spring-boot-starter-biz-tenant</module>
    <module>tro-spring-boot-starter-biz-data-permission</module>
    <module>tro-spring-boot-starter-biz-ip</module>
    <module>tro-spring-boot-starter-signature</module>
  </modules>
</project>
