package cn.zhuoxiang.tro.module.infra.controller.app.config;

import cn.zhuoxiang.tro.framework.common.pojo.CommonResult;
import cn.zhuoxiang.tro.module.infra.dal.dataobject.config.ConfigDO;
import cn.zhuoxiang.tro.module.infra.enums.ErrorCodeConstants;
import cn.zhuoxiang.tro.module.infra.service.config.ConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static cn.zhuoxiang.tro.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.zhuoxiang.tro.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 参数配置")
@RestController
@RequestMapping("/infra/config")
@Validated
public class AppConfigController {

    @Resource
    private ConfigService configService;

    @GetMapping(value = "/get-value-by-key")
    @Operation(summary = "根据参数键名查询参数值", description = "不可见的配置，不允许返回给前端")
    @Parameter(name = "key", description = "参数键", required = true, example = "yunai.biz.username")
    public CommonResult<String> getConfigKey(@RequestParam("key") String key) {
        ConfigDO config = configService.getConfigByKey(key);
        if (config == null) {
            return success(null);
        }
        if (!config.getVisible()) {
            throw exception(ErrorCodeConstants.CONFIG_GET_VALUE_ERROR_IF_VISIBLE);
        }
        return success(config.getValue());
    }

}
