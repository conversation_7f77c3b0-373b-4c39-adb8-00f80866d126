package cn.zhuoxiang.tro.framework.signature.bo.jzq;

import lombok.Data;

/**
 * 用于表示合同查看请求的请求参数。
 */
@Data
public class LinkAnonyDetailRequest {
    /**
     * 合同编号。
     * 必填项。
     */
    private String applyNo;

    /**
     * PC端查看合同模式。
     * 可选项，默认为不设置。
     * 1表示查看合同页面隐藏两边信息。
     */
    private Integer viewMode;

    /**
     * 使用自定义域名。
     * 可选项，默认为不设置。
     * 1表示使用自定义域名，注：嵌入小程序推荐设置该参数。
     */
    private String useCustomDomain;
}
