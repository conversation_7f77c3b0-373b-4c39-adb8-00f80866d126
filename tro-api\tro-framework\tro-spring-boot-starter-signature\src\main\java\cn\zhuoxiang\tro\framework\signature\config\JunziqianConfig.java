package cn.zhuoxiang.tro.framework.signature.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 君子签 配置项
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "junziqian")
@Component
public class JunziqianConfig {
    private Map<String, JunziqianProperties> configMap;

    // Getter and Setter
    public Map<String, JunziqianProperties> getConfigMap() {
        return configMap;
    }

    public void setConfigMap(Map<String, JunziqianProperties> configMap) {
        this.configMap = configMap;
    }
}
