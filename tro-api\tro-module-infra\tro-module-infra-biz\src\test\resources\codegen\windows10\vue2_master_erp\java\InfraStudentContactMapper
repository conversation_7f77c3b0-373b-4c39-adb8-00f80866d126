package cn.zhuoxiang.tro.module.infra.dal.mysql.demo;

import java.util.*;

import cn.zhuoxiang.tro.framework.common.pojo.PageResult;
import cn.zhuoxiang.tro.framework.common.pojo.PageParam;
import cn.zhuoxiang.tro.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.zhuoxiang.tro.framework.mybatis.core.mapper.BaseMapperX;
import cn.zhuoxiang.tro.module.infra.dal.dataobject.demo.InfraStudentContactDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生联系人 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraStudentContactMapper extends BaseMapperX<InfraStudentContactDO> {

    default PageResult<InfraStudentContactDO> selectPage(PageParam reqVO, Long studentId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfraStudentContactDO>()
            .eq(InfraStudentContactDO::getStudentId, studentId)
            .orderByDesc(InfraStudentContactDO::getId));
    }

    default int deleteByStudentId(Long studentId) {
        return delete(InfraStudentContactDO::getStudentId, studentId);
    }

}